<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>QUERY.PERSISTENCE</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.7"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7"/>
    </ItemGroup>


</Project>
